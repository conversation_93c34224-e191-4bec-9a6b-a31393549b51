setInterval(() => {
  const date = new Date();

  // Get all time components
  const seconds = date.getSeconds();
  const minutes = date.getMinutes();
  const hours = date.getHours();

  console.log(`${hours}:${minutes}:${seconds}`);

  const secHand = document.querySelector("#secHand");
  const minHand = document.querySelector("#minHand");
  const hourHand = document.querySelector("#hrHand");
  //   secHand.classList.add("animate-spin");
  const secondsDegrees = (seconds / 60) * 360; 
  secHand.style.transform = `rotate(${secondsDegrees}deg)`;
  const minutesDegrees = (minutes / 60) * 360; 
  minHand.style.transform = `rotate(${minutesDegrees}deg)`;
  const hoursDegrees = (hours / 60) * 360; 
  hourHand.style.transform = `rotate(${hoursDegrees}deg)`;

  // secondHand.style.transform = `rotate(${secondsDegrees}deg)`;
}, 1000);
