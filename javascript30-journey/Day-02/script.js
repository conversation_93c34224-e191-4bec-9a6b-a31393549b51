setInterval(() => {
  const date = new Date();

  // Get all time components
  const seconds = date.getSeconds();
  const minutes = date.getMinutes();
  const hours = date.getHours();

  console.log(`${hours}:${minutes}:${seconds}`);

  const secHand=document.querySelector("#secHand");
//   secHand.classList.add("animate-spin");
const secondsDegrees = ((seconds / 60) * 360) ; // +90 to start at 12 o'clock
  secHand.style.transform=(`rotate(${secondsDegrees}deg)`);

  // secondHand.style.transform = `rotate(${secondsDegrees}deg)`;
}, 1000);
