# ⚡ **30 Days of JavaScript: JavaScript30 by <PERSON>**

Welcome to my **30 Days of JavaScript** challenge! This repository documents my journey through the [JavaScript30](https://javascript30.com/) course by <PERSON> — a hands-on series of 30 real projects using vanilla JavaScript. No libraries, no frameworks, no compilers — just pure JS!

---
## 🔗 Live Preview

Explore all 30 projects live on GitHub Pages:

🌐 **[Live Site – JavaScript30 Projects](https://itx-prash.github.io/30daysofvanillajs/)**

> Each project is built using vanilla JavaScript and styled with Tailwind CSS v4+.

---
## 🚀 **Why This Challenge?**

- To go beyond theory and apply JavaScript in real mini-projects.
- To strengthen DOM manipulation, event handling, and core JS skills.
- To build 30 fun, useful, and interactive things in 30 days.
- To stay consistent and hands-on in my learning journey.

## 📅 **Day-by-Day Progress**

| Day | Project Title                          | Summary (What I Did & Learned)                                                                                   | Link                          |
| --- | -------------------------------------- | ----------------------------------------------------------------------------------------------------------------- | ----------------------------- |
| 01  | JavaScript Drum Kit                    | Practiced DOM manipulation and event handling to trigger audio and add visual effects.                            | [Day 1 Insights](./Day-01/)   |
| 02  | JS and CSS Clock                       |                                                                                                                   | [Day 2 Insights](./Day-02/)   |
| 03  | Playing with CSS Variables             |                                                                                                                   | [Day 3 Insights](./Day-03/)   |
| 04  | Array Cardio Day 1                     |                                                                                                                   | [Day 4 Insights](./Day-04/)   |
| 05  | Flex Panel Gallery                     |                                                                                                                   | [Day 5 Insights](./Day-05/)   |
| 06  | Type Ahead                             |                                                                                                                   | [Day 6 Insights](./Day-06/)   |
| 07  | Array Cardio Day 2                     |                                                                                                                   | [Day 7 Insights](./Day-07/)   |
| 08  | Fun with HTML5 Canvas                  |                                                                                                                   | [Day 8 Insights](./Day-08/)   |
| 09  | Dev Tools Domination                   |                                                                                                                   | [Day 9 Insights](./Day-09/)   |
| 10  | Hold Shift and Check Checkboxes        |                                                                                                                   | [Day 10 Insights](./Day-10/)  |
| 11  | Custom Video Player                    |                                                                                                                   | [Day 11 Insights](./Day-11/)  |
| 12  | Key Sequence Detection                 |                                                                                                                   | [Day 12 Insights](./Day-12/)  |
| 13  | Slide in on Scroll                     |                                                                                                                   | [Day 13 Insights](./Day-13/)  |
| 14  | JavaScript References vs. Copying      |                                                                                                                   | [Day 14 Insights](./Day-14/)  |
| 15  | LocalStorage and Event Delegation      |                                                                                                                   | [Day 15 Insights](./Day-15/)  |
| 16  | CSS Text Shadow Mouse Move Effect      |                                                                                                                   | [Day 16 Insights](./Day-16/)  |
| 17  | Sort Without Articles                  |                                                                                                                   | [Day 17 Insights](./Day-17/)  |
| 18  | Adding Up Times with Reduce            |                                                                                                                   | [Day 18 Insights](./Day-18/)  |
| 19  | Webcam Fun                             |                                                                                                                   | [Day 19 Insights](./Day-19/)  |
| 20  | Speech Detection                       |                                                                                                                   | [Day 20 Insights](./Day-20/)  |
| 21  | Geolocation                           |                                                                                                                   | [Day 21 Insights](./Day-21/)  |
| 22  | Follow Along Links                     |                                                                                                                   | [Day 22 Insights](./Day-22/)  |
| 23  | Speech Synthesis                       |                                                                                                                   | [Day 23 Insights](./Day-23/)  |
| 24  | Sticky Nav                             |                                                                                                                   | [Day 24 Insights](./Day-24/)  |
| 25  | Event Capture, Propagation, Bubbling   |                                                                                                                   | [Day 25 Insights](./Day-25/)  |
| 26  | Stripe Follow Along Dropdown           |                                                                                                                   | [Day 26 Insights](./Day-26/)  |
| 27  | Click and Drag                         |                                                                                                                   | [Day 27 Insights](./Day-27/)  |
| 28  | Video Speed Controller                 |                                                                                                                   | [Day 28 Insights](./Day-28/)  |
| 29  | Countdown Timer                        |                                                                                                                   | [Day 29 Insights](./Day-29/)  |
| 30  | Whack A Mole                           |                                                                                                                   | [Day 30 Insights](./Day-30/)  |

## 🔧 **What’s Unique About JavaScript30?**

- No libraries or frameworks.
- Focuses entirely on vanilla JavaScript.
- Builds real and interactive UIs with just JS.
- Encourages experimentation and customization.

## 🧠 **What I Plan to Do**

- Document code and explain logic in simple terms.
- Provide a live preview site to interact with completed projects.
- Push updates to GitHub daily for accountability.

---


## 🌐 **Resources I Use Alongside**

- [JavaScript.info](https://javascript.info/)
- [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
- [Wes Bos JavaScript30](https://javascript30.com/)
- [YouTube](https://www.youtube.com/)
- [ChatGPT](https://chat.openai.com/)

---

## 🔗 **How to Follow My Journey?**

- **Twitter:** [@ITx_prash](https://twitter.com/ITx_prash) for daily updates.
- **GitHub:** Bookmark this repository for all insights.

---

## 📝 **License**

This project is licensed under the MIT License.

Feel free to explore, fork, and follow along!


